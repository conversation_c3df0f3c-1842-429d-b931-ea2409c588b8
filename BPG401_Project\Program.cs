﻿using System;

class BPG401_Project
{
    // الثوابت: أسماء الأشكال (غير حساسة لحالة الأحرف)
    static string[] ShapeNames = { "Circle", "Cylinder", "Triangle", "Quadrilateral", "Pentagon" };

    static void PrintSystemInfo()
    {
        Console.WriteLine("Windows version: " + Environment.OSVersion.ToString());
        Console.WriteLine("64 Bit operating system? : " + (Environment.Is64BitOperatingSystem ? "Yes" : "No"));
        Console.WriteLine("PC Name : " + Environment.MachineName);
        Console.WriteLine("Number of CPUS : " + Environment.ProcessorCount);
        Console.WriteLine("Windows folder : " + Environment.GetFolderPath(Environment.SpecialFolder.Windows));
        Console.Write("Logical Drives Available : ");
        foreach (var d in Environment.GetLogicalDrives()) Console.Write(d + " ");
        Console.WriteLine("\n---");
    }

    static int ShapeSidesCount(int shapeId)
    {
        // shapeId: 1..5 mapping to sides count (Circle=1, Cylinder=2, Triangle=3, Quadrilateral=4, Pentagon=5)
        switch (shapeId)
        {
            case 1: return 1;
            case 2: return 2;
            case 3: return 3;
            case 4: return 4;
            case 5: return 5;
            default: return 0;
        }
    }

    static int[] GenerateSides(int sidesCount, Random rnd)
    {
        int[] sides = new int[sidesCount];
        for (int i = 0; i < sidesCount; i++)
        {
            sides[i] = rnd.Next(1, 21); // 1..20 inclusive
        }
        return sides;
    }

    static bool IsShapeVerifiable(int shapeId, int[] sides)
    {
        // دائرة/أسطوانة: كافة القيم موجبة => قابلة
        if (shapeId == 1 || shapeId == 2)
        {
            for (int i = 0; i < sides.Length; i++)
                if (sides[i] <= 0) return false;
            return true;
        }
        // لأشكال متعددة الأضلاع: كل ضلع يجب أن يكون < مجموع الباقي
        int sum = 0;
        for (int i = 0; i < sides.Length; i++) sum += sides[i];
        for (int i = 0; i < sides.Length; i++)
        {
            if (sides[i] >= sum - sides[i]) return false;
        }
        return true;
    }

    static bool IsNameMatch(int shapeId, string proposed)
    {
        if (proposed == null) return false;
        proposed = proposed.Trim();
        if (proposed.Equals("", StringComparison.Ordinal)) return false;
        if (proposed.Equals("IGNORE", StringComparison.OrdinalIgnoreCase)) return false;
        string actual = ShapeNames[shapeId - 1];
        return proposed.Equals(actual, StringComparison.OrdinalIgnoreCase);
    }

    static int EvaluateQuestion(bool nameCorrect, bool sumCorrect, bool flagCorrect)
    {
        int correctCount = 0;
        if (nameCorrect) correctCount++;
        if (sumCorrect) correctCount++;
        if (flagCorrect) correctCount++;
        if (correctCount == 3) return 4;
        if (correctCount == 2) return 3;
        if (correctCount == 1) return 2;
        return 1;
    }

    static void ShowMenu()
    {
        Console.WriteLine("\n--- Statistics Menu ---");
        Console.WriteLine("1. Show all generated shapes");
        Console.WriteLine("2. Show count of fully correct answers (score==4)");
        Console.WriteLine("3. Show count of fully wrong answers (score==1)");
        Console.WriteLine("4. Show relative correctness summary");
        Console.WriteLine("5. Show user answers alongside correct answers");
        Console.WriteLine("6. Show shapes that are verifiable");
        Console.WriteLine("Q. Quit");
        Console.Write("Choose an option: ");
    }

    static void Main()
    {
        PrintSystemInfo();

        // قراءة عدد الأسئلة
        int questionCount = 0;
        while (true)
        {
            Console.Write("Enter number of questions to generate (integer >= 1): ");
            string qin = Console.ReadLine();
            if (int.TryParse(qin, out questionCount) && questionCount >= 1) break;
            Console.WriteLine("Invalid input. Please enter a positive integer >= 1.");
        }

        // قراءة اسم المستخدم ورقمه (بسيط حسب المتطلبات)
        string userName = "";
        string userId = "";
        while (true)
        {
            Console.Write("Enter your name (at least 6 chars, letters/digits allowed): ");
            userName = Console.ReadLine();
            if (!string.IsNullOrEmpty(userName) && userName.Length >= 6) break;
            Console.WriteLine("Name must be at least 6 characters.");
        }
        while (true)
        {
            Console.Write("Enter your ID (at least 6 chars): ");
            userId = Console.ReadLine();
            if (!string.IsNullOrEmpty(userId) && userId.Length >= 6) break;
            Console.WriteLine("ID must be at least 6 characters.");
        }

        // تخصيص المصفوفات
        int[] generatedShapeId = new int[questionCount];
        string[] generatedShapeName = new string[questionCount];
        int[] generatedSidesCount = new int[questionCount];
        int[][] generatedSides = new int[questionCount][];
        int[] generatedSidesSum = new int[questionCount];

        string[] userProposedName = new string[questionCount];
        int[] userSumInput = new int[questionCount]; // -1 = IGNORE or invalid
        int[] userFlagInput = new int[questionCount]; // 1 = not verifiable, 2 = verifiable, -1 = IGNORE/invalid

        bool[] nameCorrect = new bool[questionCount];
        bool[] sumCorrect = new bool[questionCount];
        bool[] flagCorrect = new bool[questionCount];
        int[] questionScore = new int[questionCount];

        Random rnd = new Random();

        Console.WriteLine("\n--- Start Questions ---");
        for (int i = 0; i < questionCount; i++)
        {
            Console.WriteLine("\nQuestion #{0}", i + 1);
            int sid = rnd.Next(1, 6); // 1..5
            generatedShapeId[i] = sid;
            generatedShapeName[i] = ShapeNames[sid - 1];
            int sidesCount = ShapeSidesCount(sid);
            generatedSidesCount[i] = sidesCount;
            generatedSides[i] = GenerateSides(sidesCount, rnd);
            int ssum = 0;
            for (int k = 0; k < generatedSides[i].Length; k++) ssum += generatedSides[i][k];
            generatedSidesSum[i] = ssum;

            // نطلب من المستخدم الإجابة (يمكنه كتابة IGNORE لمعرفة أنها ستسجل خاطئة)
            Console.Write("Proposed shape name (or IGNORE): ");
            string pname = Console.ReadLine();
            userProposedName[i] = pname;

            Console.Write("Enter sum of sides (integer) or IGNORE: ");
            string sumIn = Console.ReadLine();
            int parsedSum = -1;
            if (!string.IsNullOrEmpty(sumIn) && !sumIn.Equals("IGNORE", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(sumIn, out parsedSum)) userSumInput[i] = parsedSum;
                else userSumInput[i] = -1;
            }
            else
            {
                userSumInput[i] = -1;
            }

            Console.Write("Enter 1 if NOT verifiable, 2 if verifiable (or IGNORE): ");
            string flagIn = Console.ReadLine();
            int parsedFlag = -1;
            if (!string.IsNullOrEmpty(flagIn) && !flagIn.Equals("IGNORE", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(flagIn, out parsedFlag)) userFlagInput[i] = parsedFlag;
                else userFlagInput[i] = -1;
            }
            else userFlagInput[i] = -1;

            // تحقق فعلي
            bool actualVerifiable = IsShapeVerifiable(sid, generatedSides[i]);
            bool nCorrect = IsNameMatch(sid, pname);
            bool sumC = (userSumInput[i] == generatedSidesSum[i]);
            bool fCorrect = (userFlagInput[i] == (actualVerifiable ? 2 : 1));

            nameCorrect[i] = nCorrect;
            sumCorrect[i] = sumC;
            flagCorrect[i] = fCorrect;
            questionScore[i] = EvaluateQuestion(nCorrect, sumC, fCorrect);

            Console.WriteLine("--- Result for question #{0} ---", i + 1);
            Console.WriteLine("Generated shape (hidden from user during question): {0}", generatedShapeName[i]);
            Console.Write("Sides: ");
            for (int t = 0; t < generatedSides[i].Length; t++) Console.Write(generatedSides[i][t] + " ");
            Console.WriteLine("\nActual sum = {0}, Verifiable = {1}", generatedSidesSum[i], actualVerifiable ? "Yes" : "No");
            Console.WriteLine("Your answers: name={0}, sum={1}, flag={2}", userProposedName[i], (userSumInput[i] == -1 ? "IGNORE" : userSumInput[i].ToString()), (userFlagInput[i] == -1 ? "IGNORE" : userFlagInput[i].ToString()));
            Console.WriteLine("Correctness: name={0}, sum={1}, flag={2}", nCorrect, sumC, fCorrect);
            Console.WriteLine("Score for Q{0} = {1}", i + 1, questionScore[i]);
        }

        // القسم الثاني: واجهة احصائية
        bool exit = false;
        while (!exit)
        {
            ShowMenu();
            string opt = Console.ReadLine();
            if (string.IsNullOrEmpty(opt)) opt = "";
            opt = opt.Trim();
            if (opt.Equals("Q", StringComparison.OrdinalIgnoreCase)) { exit = true; continue; }

            switch (opt)
            {
                case "1":
                    Console.WriteLine("\n--- All Generated Shapes ---");
                    for (int i = 0; i < questionCount; i++)
                    {
                        Console.WriteLine("Q{0}: {1}, Sides({2}) Sum={3}", i + 1, generatedShapeName[i], generatedSidesCount[i], generatedSidesSum[i]);
                        Console.Write("    Sides values: ");
                        for (int k = 0; k < generatedSides[i].Length; k++) Console.Write(generatedSides[i][k] + " ");
                        Console.WriteLine();
                    }
                    break;
                case "2":
                    {
                        int cnt = 0;
                        for (int i = 0; i < questionCount; i++) if (questionScore[i] == 4) cnt++;
                        Console.WriteLine("Fully correct answers (score==4): {0}", cnt);
                    }
                    break;
                case "3":
                    {
                        int cnt = 0;
                        for (int i = 0; i < questionCount; i++) if (questionScore[i] == 1) cnt++;
                        Console.WriteLine("Fully wrong answers (score==1): {0}", cnt);
                    }
                    break;
                case "4":
                    {
                        int atLeastTwo = 0;
                        for (int i = 0; i < questionCount; i++) if (questionScore[i] >= 3) atLeastTwo++;
                        double pct = (double)atLeastTwo / (double)questionCount * 100.0;
                        Console.WriteLine("Questions with at least two correct answers: {0} / {1} ({2:0.00}%)", atLeastTwo, questionCount, pct);
                    }
                    break;
                case "5":
                    {
                        Console.WriteLine("\n--- User answers vs correct answers ---");
                        for (int i = 0; i < questionCount; i++)
                        {
                            Console.WriteLine("Q{0}: Generated={1} (sum={2}, verifiable={3})", i + 1, generatedShapeName[i], generatedSidesSum[i], IsShapeVerifiable(generatedShapeId[i], generatedSides[i]));
                            Console.WriteLine("    Your: name={0} [{1}], sum={2} [{3}], flag={4} [{5}]", userProposedName[i], nameCorrect[i], (userSumInput[i]==-1?"IGNORE":userSumInput[i].ToString()), sumCorrect[i], (userFlagInput[i]==-1?"IGNORE":userFlagInput[i].ToString()), flagCorrect[i]);
                            Console.WriteLine("    Score={0}", questionScore[i]);
                        }
                    }
                    break;
                case "6":
                    {
                        Console.WriteLine("\n--- Verifiable shapes list ---");
                        for (int i = 0; i < questionCount; i++)
                        {
                            bool v = IsShapeVerifiable(generatedShapeId[i], generatedSides[i]);
                            if (v) Console.WriteLine("Q{0}: {1} (sum={2})", i + 1, generatedShapeName[i], generatedSidesSum[i]);
                        }
                    }
                    break;
                default:
                    Console.WriteLine("Unknown option. Please choose 1-6 or Q to quit.");
                    break;
            }
        }

        Console.WriteLine("Program finished. Thank you!");
    }
}
