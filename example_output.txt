مثال مخرجات تشغيل مختصر (مقتطف):
Windows version: Microsoft Windows NT 10.0.19043.0
64 Bit operating system? : Yes
PC Name : STUDENT-PC
Number of CPUS : 4
Windows folder : C:\Windows
Logical Drives Available : C:,D:
---
Enter number of questions to generate (integer >= 1): 2
Enter your name (at least 6 chars, letters/digits allowed): student
Enter your ID (at least 6 chars): 202500
--- Start Questions ---

Question #1
Proposed shape name (or IGNORE): triangle
Enter sum of sides (integer) or IGNORE: 15
Enter 1 if NOT verifiable, 2 if verifiable (or IGNORE): 2
--- Result for question #1 ---
Generated shape (hidden from user during question): Triangle
Sides: 5 5 5
Actual sum = 15, Verifiable = True
Your answers: name=triangle, sum=15, flag=2
Correctness: name=True, sum=True, flag=True
Score for Q1 = 4

Question #2
Proposed shape name (or IGNORE): IGNORE
Enter sum of sides (integer) or IGNORE: IGNORE
Enter 1 if NOT verifiable, 2 if verifiable (or IGNORE): IGNORE
--- Result for question #2 ---
Generated shape (hidden from user during question): Pentagon
Sides: 6 6 6 6 6
Actual sum = 30, Verifiable = True
Your answers: name=IGNORE, sum=IGNORE, flag=IGNORE
Correctness: name=False, sum=False, flag=False
Score for Q2 = 1

(بعد انتهاء الأسئلة، تظهر قائمة احصائية متاحة)
